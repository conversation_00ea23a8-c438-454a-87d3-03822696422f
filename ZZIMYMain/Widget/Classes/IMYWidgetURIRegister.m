//
//  IMYWidgetURIRegister.m
//  ZZIMYMain
//
//  Created by huang<PERSON><PERSON><PERSON> on 2025/8/14.
//

#import "IMYWidgetURIRegister.h"
#import <IMYBaseKit/IMYPublic.h>
@import IMYSwift;
#if __has_include(<SeeyouWidgetService/IMYWidgetBridgeHelper.h>)
#import <SeeyouWidgetService/IMYWidgetBridgeHelper.h>
#endif

@implementation IMYWidgetURIRegister

IMY_KYLIN_FUNC(IMY_KYLIN_STAGE_PREMAIN, 2, IMY_KYLIN_QUEUE_ASYNC) {
    [IMYWidgetURIRegister registerWidgetAction];
    if (@available(iOS 16.1, *)) {
        [IMYWidgetURIRegister registerLiveActivityAction];
    }
}

+ (void)registerWidgetAction {
    
    /// @uri_start
    ///
    /// @name   widget/reload/allWidgets
    /// @brief  widget 小组件刷新
    [[IMYURIManager shareURIManager] addForPath:@"widget/reload/allWidgets" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (@available(iOS 14.0, *)) {
            [IMYWidgetKitHelper reloadAllWidgets];
        }
    }];
    
    /// @uri_start
    ///
    /// @name   widget/reload/allWidgets
    /// @brief  widget 喂养小组件刷新
    [[IMYURIManager shareURIManager] addForPath:@"widget/reload/allBabyFeedWidgets" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (@available(iOS 14.0, *)) {
            [IMYWidgetKitHelper reloadWidgetForKindWithKindStr:@"weiyang_free_01"];
            [IMYWidgetKitHelper reloadWidgetForKindWithKindStr:@"weiyang_vip_01"];
            [IMYWidgetKitHelper reloadWidgetForKindWithKindStr:@"weiyang_vip_02"];
        }
    }];
    
    /// @uri_start
    ///
    /// @name   widget/reload/allWidgets
    /// @brief  widget 指定小组件刷新
    [[IMYURIManager shareURIManager] addForPath:@"widget/reload/widget" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (@available(iOS 14.0, *)) {
            NSString *widgetKind = actionObject.uri.params[@"widgetKind"];
            if (imy_isNotBlankString(widgetKind)) {
                [IMYWidgetKitHelper reloadWidgetForKindWithKindStr:widgetKind];
            }
        }
    }];
}

+ (void)registerLiveActivityAction {
    
    /// @uri_start
    ///
    /// @name   liveAcitivty/sleep
    /// @brief  实时活动-睡眠
    [[IMYURIManager shareURIManager] addForPath:@"liveAcitivty/sleep" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
#if __has_include(<SeeyouWidgetService/IMYWidgetBridgeHelper.h>)
        [IMYWidgetBridgeHelper handleSleepLiveAcitivtyWithParams:actionObject.uri.params];
#endif
    }];
    
    /// @uri_start
    ///
    /// @name   liveAcitivty/breastMilk
    /// @brief  实时活动-母乳
    [[IMYURIManager shareURIManager] addForPath:@"liveAcitivty/breastMilk" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
#if __has_include(<SeeyouWidgetService/IMYWidgetBridgeHelper.h>)
        [IMYWidgetBridgeHelper handleBreastMilkLiveAcitivtyWithParams:actionObject.uri.params];
#endif
    }];
    
    /// @uri_start
    ///
    /// @name   liveAcitivty/fmCounting
    /// @brief  实时活动-数胎动
    [[IMYURIManager shareURIManager] addForPath:@"liveAcitivty/fmCounting" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
#if __has_include(<SeeyouWidgetService/IMYWidgetBridgeHelper.h>)
        [IMYWidgetBridgeHelper handleFMCountingLiveAcitivtyWithParams:actionObject.uri.params];
#endif
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"liveAcitivty/checkLiveActivitys" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
#if __has_include(<SeeyouWidgetService/IMYWidgetBridgeHelper.h>)
        NSMutableArray *liveActivityArray = [NSMutableArray new];
        NSArray *arr = actionObject.uri.params[@"liveActivityArray"];
        if (arr && [arr isKindOfClass:[NSArray class]]) {
            [liveActivityArray addObjectsFromArray:arr];
        }
        [IMYWidgetBridgeHelper checkShowLiveActivitys:liveActivityArray];
#endif
    }];
}

@end
