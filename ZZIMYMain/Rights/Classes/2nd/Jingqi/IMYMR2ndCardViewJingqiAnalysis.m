//
//  IMYMR2ndCardViewJingqiAnalysis.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/11.
//

#import "IMYMR2ndCardViewJingqiAnalysis.h"

@implementation IMYMR2ndCardViewJingqiAnalysis

IMYHIVE_REGIST_CLASS(IMYMR2ndCardViewJingqiAnalysis, IOCMR2ndCardViewProcotol);

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    // 卡片分类：1经期健康度2黄金受孕期3好孕计划4产检单解读5喂奶6睡眠7发育测评
    NSInteger const type = [cardData[@"type"] integerValue];
    return type == 1;
}

- (void)onViewDidLoad {
    // 设置标题icon
    UIImage *icon = [UIImage imageNamed:@"vip_2nd_icon_jingqi_analysis"];
    [self setupWithIcon:icon andTitle:@"经期健康度"];
    
    // 直接全部重建
    [self.contentView imy_removeAllSubviews];
    
    UIImageView *scoreImageView = [UIImageView new];
    scoreImageView.frame = CGRectMake(12, 0, 76, 76);
    [self.contentView addSubview:scoreImageView];
    
    UILabel *scoreLabel = [UILabel new];
    [self.contentView addSubview:scoreLabel];
    
    UILabel *bottomLabel = [UILabel new];
    bottomLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    bottomLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    bottomLabel.text = @"预测得分";
    [bottomLabel imy_sizeToFit];
    bottomLabel.imy_height = 16;
    bottomLabel.imy_centerX = scoreImageView.imy_centerX;
    bottomLabel.imy_bottom = self.contentView.imy_height - 4;
    [self.contentView addSubview:bottomLabel];
    
    // 内容数据
    NSDictionary * const contentData = self.rawData[@"data"];
    
    // 进度图片
    NSInteger const score = [contentData[@"score"] integerValue];
    if (score > 0) {
        NSString *barImageKey = nil;
        if (score >= 94) {
            barImageKey = @"mr_2nd_jqjkd_95_99";
        } else if (score >= 86) {
            barImageKey = @"mr_2nd_jqjkd_90_95";
        } else if (score >= 78) {
            barImageKey = @"mr_2nd_jqjkd_85_90";
        } else if (score >= 70) {
            barImageKey = @"mr_2nd_jqjkd_80_85";
        } else if (score >= 62) {
            barImageKey = @"mr_2nd_jqjkd_75_80";
        } else if (score >= 42) {
            barImageKey = @"mr_2nd_jqjkd_70_75";
        } else {
            barImageKey = @"mr_2nd_jqjkd_0";
        }
        scoreImageView.image = [UIImage imageNamed:barImageKey];
        
        scoreLabel.font = [UIFont systemFontOfSize:19 weight:UIFontWeightMedium];
        scoreLabel.text = [NSString stringWithFormat:@"%ld", score];
        scoreLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        [scoreLabel imy_sizeToFit];
        scoreLabel.center = scoreImageView.center;
        
        // 获取标签最多两项
        NSArray * const mens_health = contentData[@"mens_health"];
        for (NSInteger index = 0; index < mens_health.count && index < 2; index ++) {
            NSDictionary * const item = mens_health[index];
            
            NSString * const text = item[@"text"];
            NSString * const label = item[@"label"];
            NSInteger const labelColor = [item[@"label_color"] integerValue];
            
            UILabel *textLabel = [UILabel new];
            textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
            textLabel.textColor = UIColor.whiteColor;
            textLabel.text = text.length > 0 ? text : @"";
            textLabel.frame = CGRectMake(110, index == 0 ? 18 : 40, 0, 18);
            [textLabel imy_sizeToFitWidth];
            [self.contentView addSubview:textLabel];
            
            UILabel *tagLabel = [UILabel new];
            tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
            tagLabel.text = label.length > 0 ? label : @"";
            tagLabel.backgroundColor = [UIColor colorWithWhite:0 alpha:0.15];
            tagLabel.textAlignment = NSTextAlignmentCenter;
            [tagLabel imy_drawAllCornerRadius:4];
            tagLabel.frame = CGRectMake(0, 0, 0, 16);
            [tagLabel imy_sizeToFitWidth];
            tagLabel.imy_width += 8;
            tagLabel.imy_left = textLabel.imy_right + 2;
            tagLabel.imy_centerY = textLabel.imy_centerY;
            if (!label.length) {
                tagLabel.hidden = YES;
            }
            [self.contentView addSubview:tagLabel];
            
            // 0:灰色 1:绿色 2:黄色 3:红色
            switch (labelColor) {
                case 1:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#29CC5F");
                    break;
                case 2:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#FF8833");
                    break;
                case 3:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#FF4D4D");
                    break;
                default:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#999999");
                    break;
            }
        }
    } else {
        // 兜底UI
        scoreImageView.image = [UIImage imageNamed:@"mr_2nd_jqjkd_0"];
        
        scoreLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        scoreLabel.text = @"--";
        scoreLabel.textColor = UIColor.whiteColor;
        [scoreLabel imy_sizeToFit];
        scoreLabel.center = scoreImageView.center;
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = [UIColor colorWithWhite:1 alpha:0.7];
        textLabel.numberOfLines = 2;
        textLabel.text = @"补记经期情况，为您分析经期健康";
        textLabel.frame = CGRectMake(110, 6, 138, 36);
        [self.contentView addSubview:textLabel];
        
        UILabel *gotoLabel = [UILabel new];
        gotoLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        gotoLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        gotoLabel.textAlignment = NSTextAlignmentCenter;
        gotoLabel.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        gotoLabel.layer.borderWidth = 1 / SCREEN_SCALE;
        gotoLabel.layer.cornerRadius = 12;
        gotoLabel.frame = CGRectMake(110, 46, 57, 24);
        gotoLabel.text = @"去记录";
        [self.contentView addSubview:gotoLabel];
    }
}

- (void)onViewDidClick {
    // 经期健康度
    [[IMYURIManager sharedInstance] runActionWithPath:@"secondFloorClick"
                                               params:@{ @"id" : @1007 }
                                                 info:nil];
}

- (NSDictionary *)onReportParams {
    NSDictionary * const contentData = self.rawData[@"data"];
    NSInteger const score = [contentData[@"score"] integerValue];
    
    NSString *public_type = nil;
    if (score > 0) {
        public_type = @(score).stringValue;
    } else {
        public_type = @(-1).stringValue;
    }
    return @{
        @"public_type" : public_type ?: @"",
    };
}

@end
