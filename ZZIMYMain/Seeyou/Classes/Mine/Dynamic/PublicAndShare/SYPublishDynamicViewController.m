//
//  SYPublishDynamicViewController.m
//  Seeyou
//
//  Created by <PERSON><PERSON>l<PERSON> on 14-7-7.
//  Copyright (c) 2014年 linggan. All rights reserved.
//

#import "SYPublishDynamicViewController.h"
#import "NSArray+SY.h"
#import "SYCameraImageCache.h"
#import "SYDynamicDraftModel.h"
#import "SYDynamicModel.h"
#import "SYPublishDynamicManager.h"
#import "SYPublishDynamicToolView.h"
#import "SYPublishDynamicViewController+Share.h"
#import "SZTextView.h"
#ifndef IMYYUNYUDEBUG
#import "TTQCameraButton.h"
#import <NSString+TTQ.h>
#endif
#import <IMYAccountServerURL.h>
#import <IMYBaseKit/IMYAssetManager.h>
#define kMaxImageCount 9
#define kTagFix 100


NSString *const DyanamicSaveToLocalSuccess = @"DyanamicSaveToLocalSuccess";
@interface SYPublishDynamicViewController () <UITextViewDelegate, IMYREmoticonInputViewDelegate, IMYAssetPickerControllerDelegate, UIImagePickerControllerDelegate>

@property (nonatomic, strong) NSMutableArray *cameraButtons;
@property (nonatomic, strong) NSMutableArray *assets;
@property (nonatomic, strong) UIView *emotionView;
@property (assign, nonatomic) NSUInteger lastAssetSelectedIndex;
@property (assign, nonatomic) int maxWords;
@property (copy, nonatomic) NSAttributedString *str_content; //防止内存不够释放UI时清空文本
@property (strong, nonatomic) NSTimer *saveTimer;
@property (strong, nonatomic) UIImagePickerController *imagePickerController;

@property (nonatomic, strong) SYPublishDynamicToolView *bottomToolView; //底部工具栏
@property (nonatomic, assign) CGFloat keyboardHeight;
@property (nonatomic, strong) UIView *textViewEmojiCover;
@end

@implementation SYPublishDynamicViewController
#ifndef IMYYUNYUDEBUG

IMY_KYLIN_FUNC(IMY_KYLIN_STAGE_IDLE, 2, IMY_KYLIN_QUEUE_ASYNC) {
    [[IMYURIManager shareURIManager] addForPath:@"share/dynamic"
    withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
            [[IMYURIManager shareURIManager] runActionWithString:@"login"];
            return;
        }
        SYPublishDynamicViewController *vc = [SYPublishDynamicViewController new];
        [vc imy_setPropertyWithDictionary:actionObject.uri.params];
        vc.fromURI = actionObject.uri;
        UIViewController *showAtVC = actionObject.uri.params[@"showAtVC"];
        if (showAtVC && [showAtVC isKindOfClass:[UIViewController class]]) {
            //                                            vc.shareModel = nil;
            IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:vc];
            [showAtVC presentViewController:nav animated:YES completion:nil];
        } else {
            [actionObject.getUsingViewController imy_push:vc];
        }
    }];
}

- (id)initWithShareModel:(id)model defaultIcon:(NSString *)icon type:(SYPublicDynamicType)type {
    self = [super init];
    if (self) {
        self.shareModel = model;
        self.defaultIcon = icon;
        self.type = type;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    _txt_content.attributedText = _str_content;
    [self updateTopRight];
    [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
    self.navigationController.navigationBar.translucent = NO;
}

- (void)dismissAndSaveDraft {
    [[self.navigationController.view.subviews firstObjectOfClass:[IMYActionSheet class]] removeFromSuperview];
    if ([self currentEncodeText].length > 0 || self.cameraButtons.count > 0) {
        if (self.saveTimer) {
            [self.saveTimer invalidate];
            self.saveTimer = nil;
        }
        [self saveDraft];
    }
    [self dismissViewControllerAnimated:NO completion:nil];
}

- (void)dealloc {
    if (_saveTimer) {
        [_saveTimer invalidate];
        _saveTimer = nil;
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationItem.title = IMYString(@"发动态");

    [self setupContentView];
    [self setupBottomToolView];
    [self setupPhotoView];
    //    [self contentViewHeight];

    [self.imy_topRightButton imy_setTitle:IMYString(@"发布")];
    [self.imy_topRightButton imy_setTitleColor:kCK_Red_BT];
    [self.imy_topRightButton addTarget:self action:@selector(publish:) forControlEvents:UIControlEventTouchUpInside];
    [self imy_topLeftButtonIsBack];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleWillShowKeyboardNotification:)
                                                 name:UIKeyboardWillChangeFrameNotification
                                               object:nil];


    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleWillHideKeyboardNotification:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];

    [self updateTopRight];
    IMYSwitchModel *model = [[IMYDoorManager sharedManager] switchForType:@"dynamic"];
    _maxWords = model ? model.data.words_num : 300;
    NSInteger count = [[self currentEncodeText] imy_charCount];
    [self updateCount:count];

    @weakify(self);
    [self.tableView bk_whenTapped:^{
        @strongify(self);
        if (![self.txt_content isFirstResponder]) {
            [self.txt_content becomeFirstResponder];
        };
    }];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if (self.txt_content.tag == 0) {
        self.txt_content.tag = 1;
        //        [self contentViewHeight];
        [self showOrHidenPhotoView];
        [self setBottomToolViewState:TTQPublishToolStateNormal];
    }
}

#pragma mark - views;

- (int)cameraButtonWidth {
    return (SCREEN_WIDTH - 36) / 4.0;
}

- (void)layoutCameraViews {
    for (TTQCameraButton *btn in self.cameraButtons) {
        [btn removeFromSuperview];
    }
    [self.cameraButtons removeAllObjects];
    if ([UIDevice imy_dualCore]) {
        [self layoutCameraViews1];
    } else {
        int width = [self cameraButtonWidth];
        int column = 4;
        for (int i = 0; i < self.assets.count; i++) {
            int x = i % column;
            int y = i / column;
            IMYAssetModel *asset = self.assets[i];

            TTQCameraButton *btn = [[TTQCameraButton alloc] initWithFrame:CGRectMake(15 + (width + 2) * x, (width + 2) * y, width, width)];
            btn.cacheImage = YES;
            [asset requestThumbnailImageWithSize:btn.bounds.size
                                      completion:^(UIImage *_Nonnull result, NSDictionary<NSString *, id> *_Nonnull info) {
                                          btn.thumbImage = result;
                                      }];
            [asset requestFullScreenImageWithCompletion:^(UIImage *_Nonnull result, NSDictionary<NSString *, id> *_Nonnull info) {
                btn.originalImage = result;
            }
                                     andProgressHandler:nil];
            [self.photoView addSubview:btn];
            [self.cameraButtons addObject:btn];
            btn.tag = i + kTagFix;
            [btn addTarget:self action:@selector(buttonTap:) forControlEvents:UIControlEventTouchUpInside];
        }
        [self resetPhotosOther];
    }
    [self showOrHidenPhotoView];
}

- (void)layoutCameraViews1 {
    NSInteger width = [self cameraButtonWidth];
    NSInteger column = 4;
    [self.assets enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSInteger x = (NSInteger)(idx % column);
        NSInteger y = (NSInteger)(idx / column);
        IMYAssetModel *asset = self.assets[idx];
        TTQCameraButton *btn = [[TTQCameraButton alloc] initWithFrame:CGRectMake(15 + (width + 2) * x, (width + 2) * y, width, width)];
        btn.cacheImage = YES;
        [self.cameraButtons addObject:btn];
        btn.tag = (NSInteger)(idx + kTagFix);
        [asset requestThumbnailImageWithSize:btn.bounds.size
                                  completion:^(UIImage *_Nonnull result, NSDictionary<NSString *, id> *_Nonnull info) {
            btn.thumbImage = result;
        }];
        [asset requestFullScreenImageWithCompletion:^(UIImage *_Nonnull result, NSDictionary<NSString *, id> *_Nonnull info) {
            btn.originalImage = result;
        }
                                 andProgressHandler:nil];
        [btn addTarget:self action:@selector(buttonTap:) forControlEvents:UIControlEventTouchUpInside];

    }];
    if (self.cameraButtons.count > 1) {
        [self.cameraButtons sortUsingComparator:^NSComparisonResult(TTQCameraButton *obj1, TTQCameraButton *obj2) {
            if (obj1.tag < obj2.tag || !obj1 || !obj2) {
                return NSOrderedAscending;
            } else {
                return NSOrderedDescending;
            }
        }];
    }
    [self.cameraButtons enumerateObjectsUsingBlock:^(TTQCameraButton *button, NSUInteger idx, BOOL *stop) {
        [self.photoView addSubview:button];
    }];
    [self resetPhotosOther];

}

//显示添加按钮和计算photoView高度
- (void)resetPhotosOther {
    int column = 4;
    int width = [self cameraButtonWidth];
    if (self.assets.count < kMaxImageCount) {
        TTQCameraButton *btn = [[TTQCameraButton alloc] initWithFrame:CGRectMake(15 + (width + 2) * (self.assets.count % column), (width + 2) * (self.assets.count / column), width, width)];
        btn.cacheImage = YES;
        NSString *imgName = self.assets.count == 0 ? @"sent_addphoto" : @"sent_add";
        [btn setBackgroundImage:[UIImage sy_themeImageWithName:imgName] forState:UIControlStateNormal];
        [self.photoView addSubview:btn];
        [self.cameraButtons addObject:btn];
        btn.tag = self.assets.count + kTagFix;
        [btn addTarget:self action:@selector(buttonTap:) forControlEvents:UIControlEventTouchUpInside];
    }
    CGFloat height = ((self.assets.count) / column + 1) * (width + 2) + 20; //2是行间距，width是每个按钮高度，20是下间距
    self.photoView.imy_height = height;
    [self updateTopRight];
}

- (void)updateTopRight {
    if ([self currentEncodeText].length <= 0 && self.cameraButtons.count <= 1) {
        self.imy_topRightButton.alpha = 0.5;
        self.imy_topRightButton.userInteractionEnabled = NO;
    } else {
        self.imy_topRightButton.alpha = 1;
        self.imy_topRightButton.userInteractionEnabled = YES;
    }
    if (self.shareModel) {
        self.imy_topRightButton.alpha = 1;
        self.imy_topRightButton.userInteractionEnabled = YES;
    }
}

- (void)setupContentView {
    [self.contentView imy_setBackgroundColorForKey:kCK_White_AN];
    self.txt_content.placeholder = IMYString(@"和好友们分享兴趣、心情、经验和小秘密…");
    self.txt_content.placeholderTextColor = SYThemeColor(kCK_Black_D);
    self.txt_content.delegate = self;
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    paragraphStyle.lineSpacing = 4; // 字体的行间距
    NSDictionary *attributes = @{
        NSFontAttributeName: [UIFont systemFontOfSize:17.0],
        NSParagraphStyleAttributeName: paragraphStyle
    };
    self.txt_content.typingAttributes = attributes;

    self.contentView.imy_top = 10;
    self.contentView.imy_left = 15;
    self.contentView.imy_width = self.view.imy_width - 30;
    self.contentView.imy_height = 150;

    self.textViewEmojiCover = [UIView new];
    self.textViewEmojiCover.backgroundColor = [UIColor clearColor];
    [self.contentView addSubview:self.textViewEmojiCover];
    [self.textViewEmojiCover mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.txt_content).offset(0);
        make.right.equalTo(self.txt_content).offset(0);
        make.top.equalTo(self.txt_content).offset(0);
        make.bottom.equalTo(self.txt_content).offset(0);
    }];
    self.textViewEmojiCover.hidden = YES;
    @weakify(self);
    [self.textViewEmojiCover bk_whenTapped:^{
        @strongify(self);
        self.bottomToolView.emotionBtn.selected = !self.bottomToolView.emotionBtn.isSelected;
        [self changeToEmotion:nil];
    }];

    self.tableView.tableHeaderView = self.contentView;
}

- (void)showOrHidenPhotoView {
    @weakify(self);
    imy_asyncMainBlock(0.1, ^{
        @strongify(self);
        self.tableView.tableFooterView = nil;
        if ([self isShowPhoto]) {
            //            self.tableView.tableFooterView = self.photoView;
            if (!self.photoView.superview) {
                self.photoView.imy_top = 150;
                [self.tableView addSubview:self.photoView];
            }
        }
    });
}

- (BOOL)isShowPhoto {
    if (self.shareModel) {
        return YES;
    }
    if (self.cameraButtons.count > 1) {
        return YES;
    }
    TTQCameraButton *cameraBtn = self.cameraButtons.lastObject;
    if (self.cameraButtons.count == 1 && (cameraBtn.thumbImage || cameraBtn.assetURL || cameraBtn.originalImage || cameraBtn.asset)) {
        return YES;
    }
    return NO;
}

- (void)setupPhotoView {
    self.photoView.imy_left = 0;
    self.photoView.imy_width = SCREEN_WIDTH;
    if (self.shareModel) {
        self.navigationItem.title = IMYString(@"分享到我的动态");
        _txt_content.placeholder = IMYString(@"说点分享心得吧～");
        [self.bottomToolView hidenCameraBtn];
        [self initShareView];
        [self showOrHidenPhotoView];
    } else {
        //防内存警告
        if (!self.cameraButtons) {
            self.cameraButtons = [NSMutableArray array];
        }
        if (!self.assets) {
            self.assets = [NSMutableArray array];
        }
        [self showOrHidenPhotoView];
        [self layoutCameraViews];
        [self loadDraft];
        self.saveTimer = [NSTimer scheduledTimerWithTimeInterval:10.f target:self selector:@selector(autoSave) userInfo:nil repeats:YES];
    }
    self.photoView.backgroundColor = [UIColor imy_colorForKey:kCK_White_AN];
}

- (void)setupBottomToolView {
    @weakify(self);
    self.bottomToolView = [SYPublishDynamicToolView publishToolView];
    [self.bottomToolView imy_setBackgroundColorForKey:kCK_White_AN];
    self.bottomToolView.emotionHandler = ^(TTQPublishButton *sender) {
        @strongify(self);
        sender.selected = !sender.isSelected;
        [self changeToEmotion:nil];
    };
    self.bottomToolView.cameraHandler = ^(TTQPublishButton *sender) {
        @strongify(self);
        [self endEditing];
        TTQCameraButton *btn = self.cameraButtons.lastObject;
        if (self.cameraButtons.count >= kMaxImageCount && btn.thumbImage) {
            [self showImageWith:0];
        } else {
            [self takeImage];
        }
    };
    self.bottomToolView.imy_size = CGSizeMake(SCREEN_WIDTH, 50);
    self.bottomToolView.packupHandler = ^(TTQPublishButton *sender) {
        @strongify(self);
        [self endEditing];
    };
    [self.view addSubview:self.bottomToolView];
    [self.view bringSubviewToFront:self.bottomToolView];
    [self setBottomToolViewState:TTQPublishToolStateNormal];

    if (SCREEN_TABBAR_SAFEBOTTOM_MARGIN > 1) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, self.bottomToolView.imy_height, self.bottomToolView.imy_width, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
        [view imy_setBackgroundColorForKey:kCK_White_AN];
        view.tag = -1001;
        [self.bottomToolView insertSubview:view atIndex:0];
    }
}

- (void)setBottomToolViewState:(TTQPublishToolState)publishToolState {
    self.bottomToolView.publishToolState = publishToolState;
    if (publishToolState == TTQPublishToolStateNormal) {
        self.bottomToolView.imy_height = 60;
        self.bottomToolView.imy_bottom = SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        UIView *view = [self.bottomToolView viewWithTag:-1001];
        view.imy_height = SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    } else {
        self.bottomToolView.imy_height = 50;
        self.bottomToolView.imy_bottom = self.view.imy_height - self.keyboardHeight;
        UIView *view = [self.bottomToolView viewWithTag:-1001];
        view.imy_height = 0;
    }
    self.tableView.imy_height = self.bottomToolView.imy_top;
}

#pragma mark -

- (void)endEditing {
    [self.view endEditing:YES];
}

- (NSString *)currentEncodeText {
    return [NSString filterBlankAndBlankLines:[IMYREmoticonManager encodeEmojiText:self.txt_content.attributedText.imy_trimAttributedString]];
}

//加载草稿
- (void)loadDraft {
    if (self.assets.count) {
        return;
    }
    [self cleanDraftFrom765];
    SYDynamicDraftModel *draft = [SYDynamicDraftModel getDraft];
    if (draft) {
        [self loadDraftFinish:draft];
    }
}

- (void)loadDraftFinish:(SYDynamicDraftModel *)draft {
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        [self imy_hideHUD];
        if (draft.albumUrls.count) {
            NSMutableArray *assets = [NSMutableArray array];
            for (NSString *identifer in draft.albumUrls) {
                IMYAssetModel *model = [[IMYAssetModel alloc] initWithIdentifier:identifer];
                [assets addObject:model];
            }
            self.assets = assets;
        }
        NSInteger draftCount = [draft.content imy_charCount];
        if (draftCount > self.maxWords * 2) {
            NSString *s = [draft.content imy_substringToCharCount:self.maxWords * 2];

            draft.content = s;
        }
        self.txt_content.attributedText = draft.content ? [IMYREmoticonManager decodeEmojiText:draft.content attributes:self.txt_content.typingAttributes] : nil;
        self.txt_content.textColor = [UIColor imy_colorForKey:kCK_Black_AT];
        self.str_content = self.txt_content.attributedText;
        [self layoutCameraViews];
        [self updateTopRight];
        NSInteger count = [draft.content imy_charCount];
        [self updateCount:count];
    });
}

- (void)addPhoto:(id)sender {
    [self takeImage];
}

- (void)cleanDraftFrom765 {
    NSString *cleanKey = [NSString stringWithFormat:@"cleanDyDraft_%@", [IMYPublicAppHelper shareAppHelper].userid];
    if (![[IMYUserDefaults standardUserDefaults] boolForKey:cleanKey]) {
        [SYDynamicDraftModel deleteWithWhere:nil];
        [[IMYUserDefaults standardUserDefaults] setBool:YES forKey:cleanKey];
    }
}
#pragma mark - button handle

- (void)imy_topLeftButtonTouchupInside {
    if (self.shareModel) {
        [self endEditing];
        [self dismissAction];
        return;
    }
    if ([self currentEncodeText].length <= 0 && self.cameraButtons.count <= 1) {
        [self endEditing];
        if (self.saveTimer) {
            [self.saveTimer invalidate];
            self.saveTimer = nil;
        }
        [self dismissAction];
    } else {
        [self showSaveAlert];
    }
}

- (void)dismissAction {
    if (self.type == SYPublicDynamicTypeMeeyouAccount ||
        self.type == SYPublicDynamicTypeNews ||
        self.type == SYPublicDynamicTypeSubject ||
        self.type == SYPublicDynamicTypeNovel ||
        self.type == SYPublicDynamicTypeTopic ||
        self.type == SYPublicDynamicTypeVideo) {
        [self imy_pop:TRUE];
    } else {
        [self.navigationController dismissViewControllerAnimated:YES completion:nil];
    }
}

//保存提示
- (void)showSaveAlert {
    @weakify(self)
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                 otherTitles:@[IMYString(@"保存"), IMYString(@"不保存")]
                                     summary:IMYString(@"先保存，下次继续编辑？")
                                  showInView:nil
                                      action:^(NSInteger index) {
                                          @strongify(self);
                                          if (index == 1) {
                                              [SYMobClick eventInQueue:@"bccg"];
                                              [self.saveTimer invalidate];
                                              self.saveTimer = nil;
                                              //存入草稿箱
                                              if (![self saveDraft]) {
                                                  [UIWindow imy_showTextHUD:IMYString(@"保存失败")];
                                              }
                                              [self dismissAction];
                                              [self dismissViewControllerAnimated:YES completion:nil];
                                          } else if (index == 2) {
                                              ///发送完就把界面上的数据清空掉
                                              self.txt_content.attributedText = nil;
                                              self.assets = nil;
                                              [self.saveTimer invalidate];
                                              self.saveTimer = nil;
                                              [SYDynamicDraftModel deleteWithWhere:nil];
                                              [self dismissViewControllerAnimated:YES completion:nil];
                                          }
                                      }];
}

- (BOOL)saveDraft {
    [SYDynamicDraftModel deleteWithWhere:nil];
    if ([self currentEncodeText].length <= 0 && self.assets.count == 0) {
        [SYDynamicDraftModel deleteWithWhere:nil];
        return YES;
    } else {
        SYDynamicDraftModel *model = [[SYDynamicDraftModel alloc] init];
        //保证只存一个
        model.rowid = 1;
        model.content = [self currentEncodeText];
        NSMutableArray *identifiers = [NSMutableArray array];
        for (IMYAssetModel *asset in self.assets) {
            [identifiers addObject:asset.identifier];
        }
        model.albumUrls = identifiers;
        return [model saveToDB];
    }
}

- (void)publish:(id)sender {
    [self endEditing];
    noNickNameHandle;
    [SYMobClick eventInQueue:@"fb-myq"];
    if ([[self currentEncodeText] imy_charCount] > _maxWords * 2) {
        [UIWindow imy_showTextHUD:[NSString stringWithFormat:IMYString(@"最多只能输入%d个字哦～"), _maxWords]];
        return;
    }
    if (self.shareModel) {
        [self shareDynamic];
        return;
    }
    if ([self currentEncodeText].length <= 0 && self.cameraButtons.count <= 1) {
        return;
    }
    if (![IMYNetState networkEnable]) {
        [UIView imy_showTextHUD:NetWorkDisableString];
        return;
    }

    SYDynamicModel *publish = [[SYDynamicModel alloc] init];
    publish.isvip = [SYUserHelper sharedHelper].isShowVip;
    NSMutableArray *nativeUrl = [NSMutableArray array];
    NSMutableArray *keys = [NSMutableArray array];
    [_cameraButtons bk_each:^(TTQCameraButton *button) {
        if (button.thumbImage) {
            [nativeUrl addObject:[SYCameraImageCache filePathForKey:[button getImageName]]];
            [keys addObject:[button getImageName]];
        }
    }];
    publish.user_id = [SYUserHelper sharedHelper].userid.intValue;
    publish.nativeImages = nativeUrl;
    publish.nativeImageKeys = keys;
    publish.type = SYDynamicModelTypeNormal;
    publish.content = [self currentEncodeText];
    publish.created_time = [[NSDate date] imy_getDateTimeString];
    publish.screen_name = [SYUserHelper sharedHelper].screen_name;
    publish.avatar = @{@"large": [SYUserHelper sharedHelper].headImageURL ?: @""};
    if (keys.count == 1) {
        TTQCameraButton *btn = _cameraButtons[0];
        publish.localImageSize = btn.originalImage.size;
    }
    [publish saveToDB];
    [UIWindow imy_showLoadingHUD];
    @weakify(self);
    [[SYPublishDynamicManager shareManager] addPublishData:publish
        withSuccessBlock:^{
            @strongify(self);
            [IMYGAEventHelper postWithPath:@"event"
                                    params:@{@"event":@"grzy_fdt",@"action":@2} headers:nil completed:nil];
            [UIWindow imy_hideHUD];
            //清除草稿
            [SYDynamicDraftModel deleteWithWhere:nil];
            //把界面上的数据清空掉
            self.txt_content.attributedText = nil;
            self.assets = nil;
            [self.saveTimer invalidate];
            [[NSNotificationCenter defaultCenter] postNotificationName:DyanamicSaveToLocalSuccess object:[publish imy_jsonObject]];
            self.saveTimer = nil;
            [self dismissViewControllerAnimated:YES completion:nil];
        }
        FailBlock:^(IMYWebMessageModel *failed) {
            [UIWindow imy_hideHUD];
            if (!failed) {
                [UIWindow imy_showTextHUD:IMYString(@"发布失败")];
            }
            if (failed.code == 400 || failed.code == 403) {
                if ([failed.message isEqualToString:@"ERROR_DESC_QZONE_NOT_INSTALLED"]) {
                    [UIWindow imy_showTextHUD:IMYString(@"您手机还未安装QQ软件~")];
                } else if ([failed.message isEqualToString:@"ERROR_DESC_UNAUTH"]) {
                    [UIWindow imy_showTextHUD:IMYString(@"未授权!")];
                } else {
                    [UIWindow imy_showTextHUD:failed.message];
                }
            }
            if (failed.code == 404) { //不文明词汇，敏感词汇
                [UIWindow imy_showTextHUD:failed.message];
            }
            if (failed.code == kPhoneStolenErrorCode || failed.code == kPhoneDubiousErrorCode) {
                int type = (failed.code == kPhoneStolenErrorCode) ? 1 : 2;
                [[IMYURIManager shareURIManager] runActionWithPath:@"account/phone/verify" params:@{ @"type": @(type) } info:nil];
            }
        }];
}


- (void)showImageWith:(NSInteger)currentPhotoIndex {
    @weakify(self);
    NSMutableArray *photos = [[NSMutableArray alloc] init];
    [_cameraButtons bk_each:^(TTQCameraButton *sender) {
        if (sender.thumbImage) {
            IMYPhoto *photo = [[IMYPhoto alloc] init];
            if (sender.originalImage.size.height > 0) {
                photo.image = sender.originalImage;
            } else {
                photo.image = sender.thumbImage;
            }
            photo.disableAnimation = YES;
            [photos addObject:photo];
        }
    }];

    IMYPhotoBrowser *browser = [[IMYPhotoBrowser alloc] init];
    browser.showType = IMYBrowserTypePublish;
    browser.bMultipleToolbar = YES;
    browser.photos = photos;
    browser.currentPhotoIndex = currentPhotoIndex;
    [browser setDeleteBlock:^(NSUInteger index) {
        @strongify(self);
        [self.assets removeObjectAtIndex:index];
        [self layoutCameraViews];
        //        imy_asyncMainBlock(0.1, ^{
        //            [self contentViewHeight];
        //        });
        [self updateTopRight];
    }];
    [self imy_push:browser];
}

- (void)takeImage {
    [self autoSave];
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:@[IMYString(@"从手机相册选择"), IMYString(@"拍照")]
                                 summary:nil
                              showInView:self.navigationController.view
                                  action:^(NSInteger index) {
                                      @strongify(self);
                                      if (index == 2) {
                                          if ([UIImagePickerController needAlertForType:UIImagePickerControllerSourceTypeCamera]) {
                                              [UIAlertController imy_showAlertViewWithTitle:@""
                                                                                    message:[UIImagePickerController alertStringForType:UIImagePickerControllerSourceTypeCamera]
                                                                          cancelButtonTitle:IMYString(@"确定")
                                                                          otherButtonTitles:nil
                                                                                    handler:^(UIAlertController *alertController, NSInteger buttonIndex){
                                                  
                                              }];
                                              return;
                                          }
                                          [IMYEventHelper event:@"tjtp" label:IMYString(@"拍照")];
                                          UIImagePickerController *imagePickerController = self.imagePickerController;
                                          imagePickerController.delegate = (id)self;

                                          if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
                                              imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
                                          } else {
                                              [UIWindow imy_showTextHUD:IMYString(@"您的设备没有摄像头!")];
                                              imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
                                          }

                                          [self.navigationController presentViewController:imagePickerController
                                                                                  animated:YES
                                                                                completion:^{
                                                                                    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
                                                                                }];
                                      } else if (index == 1) {
                                          [IMYEventHelper event:@"tjtp" label:@"系统相册"];
                                          [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": @"其他"}];
                                          IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
                                          vc.allowsMultipleSelection = YES;
                                          vc.delegate = self;
                                          vc.styleType = IMYAssetPickerUITypeNew;
                                          vc.selectedAssetArray = [self.assets mutableCopy];
                                          vc.maximumNumberOfSelection = 9;
                                          [self imy_present:vc animated:YES];
                                      }
                                  }];
}

- (void)buttonTap:(TTQCameraButton *)sender {
    if (sender.thumbImage) {
        [self showImageWith:sender.tag - kTagFix];
    } else {
        [self takeImage];
    }
}

#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingImage:(UIImage *)image editingInfo:(NSDictionary *)editingInfo {
    image = [image imy_fixOrientation];
    if (image) {
        [picker imy_showLoadingHUD];
        @weakify(picker, self);
        [[IMYAssetsManager sharedInstance] saveImage:image
                                   completionHandler:^(IMYAssetModel *_Nonnull model, NSError *_Nonnull error) {
                                       @strongify(picker, self);
                                       imy_asyncMainBlock(^{
                                           [picker imy_hideHUD];
                                           if (!error) {
                                               [self.assets addObject:model];
                                               [self layoutCameraViews];
                                               [self scrollToPhotoViewAnimation:YES];
                                               [picker dismissViewControllerAnimated:YES completion:nil];
                                           } else if (error.code == 2047) {
                                               [picker dismissViewControllerAnimated:YES completion:nil];
                                               [UIWindow imy_showTextHUD:IMYString(@"没有权限访问相册")];
                                           } else {
                                               [UIAlertController imy_quickAlert:IMYString(@"获取图片失败")];
                                               [picker dismissViewControllerAnimated:YES completion:nil];
                                           }
                                       });
                                   }];

    } else {
        //todo
        [UIAlertController imy_quickAlert:IMYString(@"获取图片失败")];
        [picker dismissViewControllerAnimated:YES completion:nil];
    }
    [self updateTopRight];
}


#pragma mark - IMYAssetPickerControllerDelegate
- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didSelectAssets:(NSArray<IMYAssetModel *> *)assets {
    self.assets = [NSMutableArray arrayWithArray:assets];
    [self layoutCameraViews];
    [self updateTopRight];
    [self scrollToPhotoViewAnimation:YES];
}
#pragma mark - 表情

- (UIView *)emotionView {
    if (!_emotionView) {
        _emotionView = [IMYREmoticonInputView simpleInputViewWithDelegate:self];
    }
    return _emotionView;
}

- (void)changeToEmotion:(id)sender {
    if (self.txt_content.inputView == nil) {
        self.txt_content.inputView = self.emotionView;
        self.textViewEmojiCover.hidden = NO;
    } else {
        self.txt_content.inputView = nil;
        self.textViewEmojiCover.hidden = YES;
    }
    [self.txt_content reloadInputViews];
    if (!self.txt_content.isFirstResponder) {
        [self.txt_content becomeFirstResponder];
    }
}

#pragma mark - IMYREmoticonInputViewDelegate

- (void)didTouchEmojiView:(IMYREmoticonInputView *)emojiView touchedEmoji:(NSString *)string {
    if ([[self currentEncodeText] imy_charCount] + [string imy_charCount] + 2 <= _maxWords * 2) {
        [IMYREmoticonManager emojiInsertWithTextView:self.txt_content
                                            emojiKey:string
                                     maxEmotionCount:10
                                   attributedDisplay:YES];
        [self textViewDidChange:self.txt_content];
    }
}

- (void)didDelEmojiView:(IMYREmoticonInputView *)emojiView {
    [IMYREmoticonManager emojiDeleteWithTextView:self.txt_content attributedDisplay:YES];
    [self textViewDidChange:self.txt_content];
}


#pragma mark - emotionView delegate

- (void)handleWillHideKeyboardNotification:(NSNotification *)notification {
    NSUInteger curve = [notification.userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    curve = curve << 16;
    double duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    self.keyboardHeight = 0;
    self.txt_content.inputView = nil;
    @weakify(self);
    void (^animationsBlock)(void) = ^{
        @strongify(self);
        [self setBottomToolViewState:TTQPublishToolStateNormal];
    };

    if (duration <= 0) {
        animationsBlock();
    } else {
        [UIView animateWithDuration:duration delay:0 options:curve animations:animationsBlock completion:nil];
    }
}

- (void)handleWillShowKeyboardNotification:(NSNotification *)notification {
    CGRect keyboardRect = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    if (keyboardRect.origin.y < [UIScreen mainScreen].bounds.size.height) {
        NSUInteger curve = [notification.userInfo[UIKeyboardAnimationCurveUserInfoKey] unsignedIntegerValue];
        curve = curve << 16;
        self.keyboardHeight = keyboardRect.size.height;
        double duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
        void (^animationsBlock)(void) = ^{
            TTQPublishToolState state = self.bottomToolView.publishToolState;
            if (self.bottomToolView.publishToolState == TTQPublishToolStateNormal) {
                state = TTQPublishToolStateNormalWithOnlyImage;
            }
            [self setBottomToolViewState:state];
        };

        if (duration <= 0) {
            animationsBlock();
        } else {
            [UIView animateWithDuration:duration delay:0 options:curve animations:animationsBlock completion:nil];
        }
    }
}


- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    BOOL isBreak = [IMYREmoticonManager emojiWithTextView:textView shouldChangeTextInRange:range replacementText:text];
    NSMutableAttributedString *attrs = textView.attributedText.mutableCopy;
    [attrs replaceCharactersInRange:range withString:text];
    NSString *comcatstr = [IMYREmoticonManager encodeEmojiText:attrs];
    NSInteger count = [comcatstr imy_charCount];
    if (isBreak && count > _maxWords * 2) {
        isBreak = NO;
    }
    return isBreak;
}

- (void)textViewDidBeginEditing:(UITextView *)textView {
    [self setBottomToolViewState:TTQPublishToolStateNormalWithOnlyImage];
    //    @weakify(self);
    //    imy_asyncMainBlock(0.2, ^{
    //        @strongify(self);
    //        [self contentViewHeight];
    //        [self scrollRectToTextViewForCursor];
    //    });
}

- (void)textViewDidChange:(UITextView *)textView {
    NSString *encodeText = [self currentEncodeText];
    NSInteger count = [encodeText imy_charCount];
    self.txt_content.textColor = [UIColor imy_colorForKey:kCK_Black_AT];//bugfix--textColor需要在有text内容后设置才有效
    if (count > _maxWords * 2) {
        encodeText = [encodeText imy_substringToCharCount:_maxWords * 2];
        count = [encodeText imy_charCount];
        textView.attributedText = [IMYREmoticonManager decodeEmojiText:encodeText attributes:textView.typingAttributes];
    }
    _str_content = textView.attributedText;
    [self updateTopRight];
    [self updateCount:count];
    //    @weakify(self);
    //    imy_asyncMainBlock(0.2, ^{
    //        @strongify(self);
    //        [self contentViewHeight];
    //        [self scrollRectToTextViewForCursor];
    //    });
}

#pragma mark - 自动保存

- (void)autoSave {
    _str_content = _txt_content.attributedText;
    [self saveDraft];
}

- (void)updateCount:(NSUInteger)count {
    NSInteger left = _maxWords - count / 2;
    NSLog(@"cou = %ld,left = %ld", (long)count / 2, (long)left);
    if (left < 11) {
        self.lb_count.hidden = NO;
        self.lb_count.text = [NSString stringWithFormat:@"%ld", (long)left];
        self.lb_count.textColor = left >= 0 ? SYThemeColor(kCK_Black_B) : SYThemeColor(kCK_Red_B);
    } else {
        self.lb_count.hidden = YES;
    }
}

- (UIImagePickerController *)imagePickerController {
    if (!_imagePickerController) {
        _imagePickerController = [UIImagePickerController new];
    }
    return _imagePickerController;
}

#pragma mark -
//- (CGFloat)minContentTextViewHeight {
//    CGFloat minH = SCREEN_HEIGHT - 64.0 - self.bottomToolView.imy_height - 20;
//    if (self.photoView.superview) {
//        minH -= self.photoView.imy_height;
//    }
//    return MAX(180.0, minH);
//}

//- (CGFloat)contentTextViewHeight {
//    CGFloat minH = [self minContentTextViewHeight];
//    CGFloat contentTextViewH = self.txt_content.contentSize.height + 17.0;
//    [self.txt_content setContentOffset:CGPointMake(0, 0) animated:NO];
//    return MAX(minH, contentTextViewH);
//}

//- (CGFloat)contentViewHeight {
//    self.contentView.imy_height = [self contentTextViewHeight] + 20;
//    self.tableView.tableHeaderView = self.contentView;
//    return self.contentView.imy_height;
//}

//- (void)scrollRectToTextViewForCursor {
//    UITextPosition *start = self.txt_content.selectedTextRange.start;
//    if (start) {
//        //        获取光标的位置区域
//        CGRect cursorPosition = [self.txt_content caretRectForPosition:start];
//        //         光标相对顶层视图（scrollView）frame的坐标高度
//        CGFloat height = cursorPosition.origin.y + cursorPosition.size.height + 20 - self.tableView.contentOffset.y;
//        CGFloat currentPoint = cursorPosition.origin.y + cursorPosition.size.height;
//        //         可见scrollView区域， 由于键盘有中英输入法，所以会导致可见区域的变化
//        CGFloat cursorValueOffset = self.tableView.tableFooterView ? 0 : 35;
//        CGFloat cursorValueMax = SCREEN_HEIGHT - 64 - self.keyboardHeight - 50 - 20 - cursorValueOffset - cursorPosition.size.height;
//        if (height > cursorValueMax) {
//            //            当光标在可见区域底部，即距离键盘
//            CGFloat sourceOffset = MIN(currentPoint + 20 + cursorValueOffset - cursorValueMax, self.tableView.contentSize.height - self.keyboardHeight +24 );
//            [self.tableView setContentOffset:CGPointMake(0, sourceOffset) animated:YES];
//        } else if (height < cursorPosition.size.height) {
//            //             当光标在可见区域顶部一个光标高度内，即距离顶部一个光标高度
//            [self.tableView scrollRectToVisible:CGRectMake(0, cursorPosition.origin.y + 10 - cursorPosition.size.height, SCREEN_WIDTH, 1) animated:NO];
//        }
//    }
//}

- (void)scrollToPhotoViewAnimation:(BOOL)animation {
    @weakify(self);
    imy_asyncMainBlock(0.1, ^{
        @strongify(self);
        if (self.tableView.contentSize.height > self.tableView.imy_height) {
            [self.tableView setContentOffset:CGPointMake(0, self.tableView.contentSize.height - self.tableView.imy_height)];
        }
    });
}
#endif

@end
