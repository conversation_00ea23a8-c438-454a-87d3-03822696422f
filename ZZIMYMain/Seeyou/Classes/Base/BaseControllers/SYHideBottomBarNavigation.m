//
//  SYHideBottomBarNavigation.m
//  Seeyou
//
//  Created by ljh on 15/6/17.
//  Copyright (c) 2015年 linggan. All rights reserved.
//

#import "SYHideBottomBarNavigation.h"
#import "SYGlobalMacros.h"
#import "TTQFadeTransition.h"

// 前向声明，避免循环引用
@class TTQHotTopicViewController;

@interface SYHideBottomBarNavigation ()

@end

@implementation SYHideBottomBarNavigation

- (NSArray<UIViewController *> *)popToRootViewControllerAnimated:(BOOL)animated {
    imy_asyncMainBlock(^{
        [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
    });
    return [super popToRootViewControllerAnimated:animated];
}

#pragma mark - UINavigationControllerDelegate

- (nullable id<UIViewControllerAnimatedTransitioning>)navigationController:(UINavigationController *)navigationController
                                           animationControllerForOperation:(UINavigationControllerOperation)operation
                                                        fromViewController:(UIViewController *)fromVC
                                                          toViewController:(UIViewController *)toVC {
    // 首先调用父类的实现，保持现有的转场逻辑
    id<UIViewControllerAnimatedTransitioning> parentTransition = [super navigationController:navigationController
                                                                   animationControllerForOperation:operation
                                                                              fromViewController:fromVC
                                                                                toViewController:toVC];
    if (parentTransition) {
        return parentTransition;
    }

    // 检查是否是从 TTQHotTopicViewController 返回的 pop 操作
    if (operation == UINavigationControllerOperationPop) {
        // 使用字符串比较避免直接引用，防止循环依赖
        NSString *fromVCClassName = NSStringFromClass([fromVC class]);
        if ([fromVCClassName isEqualToString:@"TTQHotTopicViewController"]) {
            // 检查是否需要使用自定义转场动画
            if ([fromVC respondsToSelector:@selector(shouldUseFadeTransition)]) {
                BOOL shouldUseFadeTransition = [[fromVC valueForKey:@"shouldUseFadeTransition"] boolValue];
                if (shouldUseFadeTransition) {
                    // 重置标识，避免影响后续操作
                    [fromVC setValue:@(NO) forKey:@"shouldUseFadeTransition"];

                    // 返回自定义渐隐转场动画
                    return [[TTQFadeTransition alloc] init];
                }
            }
        }
    }

    return nil;
}

@end
