//
//  TTQFadeTransition.m
//  Seeyou
//
//  Created by AI Assistant on 2025/08/20.
//  Copyright © 2025年 linggan. All rights reserved.
//

#import "TTQFadeTransition.h"

@implementation TTQFadeTransition

#pragma mark - UIViewControllerAnimatedTransitioning

- (NSTimeInterval)transitionDuration:(id<UIViewControllerContextTransitioning>)transitionContext {
    return 0.3; // 动画时长0.3秒
}

- (void)animateTransition:(id<UIViewControllerContextTransitioning>)transitionContext {
    // 获取转场上下文中的视图
    UIView *fromView = [transitionContext viewForKey:UITransitionContextFromViewKey];
    UIView *toView = [transitionContext viewForKey:UITransitionContextToViewKey];
    
    // 安全检查
    if (!fromView || !toView) {
        [transitionContext completeTransition:NO];
        return;
    }
    
    // 获取容器视图
    UIView *containerView = [transitionContext containerView];
    
    // 将目标视图添加到容器视图中，并放在源视图下方
    [containerView insertSubview:toView belowSubview:fromView];
    
    // 设置初始状态
    toView.alpha = 0.0;
    fromView.alpha = 1.0;
    
    // 执行渐隐动画
    [UIView animateWithDuration:[self transitionDuration:transitionContext]
                          delay:0.0
                        options:UIViewAnimationOptionCurveEaseInOut
                     animations:^{
        // 源视图渐隐，目标视图渐显
        fromView.alpha = 0.0;
        toView.alpha = 1.0;
    } completion:^(BOOL finished) {
        // 重置视图状态
        fromView.alpha = 1.0;
        toView.alpha = 1.0;
        
        // 通知转场完成
        BOOL transitionCompleted = ![transitionContext transitionWasCancelled];
        [transitionContext completeTransition:transitionCompleted];
    }];
}

@end
