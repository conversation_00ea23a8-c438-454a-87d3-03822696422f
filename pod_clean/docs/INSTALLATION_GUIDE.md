# 图片资源删除安全检查脚本 - 安装和使用指南

## 快速开始

### 1. 脚本文件
已创建的脚本文件：
- `image_deletion_safety_checker.sh` - 主脚本
- `test_image_safety_checker.sh` - 功能测试脚本
- `demo_image_safety_checker.sh` - 功能演示脚本
- `README_IMAGE_SAFETY_CHECKER.md` - 详细说明文档

### 2. 验证安装
```bash
# 运行功能测试
./test_image_safety_checker.sh

# 查看演示
./demo_image_safety_checker.sh

# 查看帮助
./image_deletion_safety_checker.sh -h
```

### 3. 基本使用
```bash
# 交互式检查所有 Pod 项目
./image_deletion_safety_checker.sh

# 检查指定 Pod 项目
./image_deletion_safety_checker.sh -p MyPodName

# 自动模式（跳过交互）
./image_deletion_safety_checker.sh -a

# 详细输出模式
./image_deletion_safety_checker.sh -v
```

## 功能概述

### 核心功能
1. **目录遍历**: 自动发现并切换到各个 Pod 项目目录
2. **Git 状态检查**: 检查工作区和暂存区中被删除的图片文件
3. **多维度引用检查**:
   - 直接文件名引用
   - 去除扩展名的基础名称引用
   - 清理分辨率后缀的名称引用
   - Asset Catalog 引用检查
   - 字符串形式的引用检查
4. **交互式确认**: 为每个文件提供详细的操作选项
5. **安全措施**: 支持文件恢复、批量处理等安全功能

### 支持的图片格式
- PNG (.png)
- JPEG (.jpg, .jpeg)
- GIF (.gif)
- SVG (.svg)
- WebP (.webp)
- BMP (.bmp)
- TIFF (.tiff)
- ICO (.ico)

### 检查的文件类型
- 代码文件: .m, .h, .swift, .mm, .cpp, .c
- 界面文件: .xib, .storyboard
- 配置文件: .plist, .json, .strings, .xml
- 文档文件: .txt, .md, .yml, .yaml

## 使用场景

### 1. Pod 优化后的二次检查
```bash
# 运行主优化脚本后
./pod_optimizer.sh -b

# 进行安全检查
./image_deletion_safety_checker.sh
```

### 2. 手动删除前的预检查
```bash
# 在手动删除图片资源前
./image_deletion_safety_checker.sh -p TargetPod
```

### 3. 定期资源清理
```bash
# 定期运行检查
./image_deletion_safety_checker.sh -a > cleanup_report.txt
```

## 交互选项说明

当脚本发现被删除的图片文件时，会提供以下选项：

1. **确认删除此文件** - 用户确认可以删除
2. **跳过此文件（保留）** - 保留文件，不删除
3. **查看更多引用详情** - 显示详细的引用信息和上下文
4. **在编辑器中打开引用文件** - 在代码编辑器中查看引用文件
5. **恢复此文件（git checkout）** - 使用 Git 恢复文件
6. **批量处理剩余文件** - 应用批量操作策略
7. **退出检查** - 停止检查过程

### 批量处理选项
- **删除所有无引用的文件** - 只删除确认无引用的文件
- **删除所有文件（忽略引用警告）** - 强制删除所有文件
- **跳过所有剩余文件** - 保留所有剩余文件
- **恢复所有剩余文件** - 恢复所有剩余文件
- **返回逐个确认模式** - 继续逐个确认

## 安全特性

### 1. 仅检查不删除
- 脚本只进行安全检查，不会自动删除文件
- 需要手动执行 Git 命令完成实际删除

### 2. 多重引用检查
- 直接引用检查
- 模糊匹配检查
- Asset Catalog 引用检查
- 字符串变体引用检查

### 3. 特殊保护
- App 图标文件特殊保护
- .appiconset 目录内容保护
- 动画序列帧检测（集成现有逻辑）

### 4. 恢复机制
- 支持单个文件恢复
- 支持批量文件恢复
- 使用 `git checkout` 安全恢复

## 性能优化

### 1. 搜索引擎
- 优先使用 ripgrep (rg) 进行高性能搜索
- 回退到 grep 作为基础搜索工具

### 2. 并发处理
- 继承现有脚本的并发处理能力
- 支持多核心并行搜索

### 3. 智能过滤
- 跳过备份和应用目录
- 智能文件类型过滤
- 避免重复检查

## 集成建议

### 1. 与现有工具集成
```bash
# 在主优化脚本中添加安全检查步骤
./pod_optimizer.sh -b
./image_deletion_safety_checker.sh -a
```

### 2. CI/CD 集成
```bash
# 在持续集成中使用
./image_deletion_safety_checker.sh -a --report > safety_check_report.txt
```

### 3. 定期维护
```bash
# 创建定期检查任务
crontab -e
# 添加: 0 2 * * 1 cd /path/to/pod_clean && ./image_deletion_safety_checker.sh -a
```

## 故障排除

### 1. 权限问题
```bash
chmod +x image_deletion_safety_checker.sh
```

### 2. 依赖缺失
```bash
# 检查 Git 是否安装
git --version

# 安装 ripgrep（可选，提升性能）
brew install ripgrep
```

### 3. 路径问题
确保在包含 Pod 项目的正确目录中运行脚本。

### 4. 性能问题
对于大型项目，可以：
- 使用 `-p` 选项检查单个 Pod
- 安装 ripgrep 提升搜索性能
- 使用 `-a` 自动模式减少交互时间

## 最佳实践

1. **备份优先**: 在大量删除前使用 `git stash` 或创建分支
2. **逐步验证**: 先在小范围内测试脚本功能
3. **定期检查**: 将脚本集成到定期维护流程中
4. **文档记录**: 记录删除决策和原因
5. **团队协作**: 在团队环境中建立统一的使用规范

## 技术支持

如遇到问题，请检查：
1. 脚本文件权限和路径
2. Git 环境和仓库状态
3. 依赖工具的安装情况
4. 运行日志和错误信息

通过运行测试脚本可以快速诊断大部分问题：
```bash
./test_image_safety_checker.sh
```
